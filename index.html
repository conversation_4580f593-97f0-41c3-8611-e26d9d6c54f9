<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Parallax Scroll Animation</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            overflow-x: hidden;
        }

        .parallax-container {
            height: 100vh;
            overflow-x: hidden;
            overflow-y: auto;
            perspective: 1px;
        }

        .parallax-layer {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
        }

        .parallax-back {
            transform: translateZ(-1px) scale(2);
        }

        .parallax-base {
            transform: translateZ(0);
        }

        /* Hero Section */
        .hero {
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            position: relative;
        }

        .hero h1 {
            font-size: 4rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .hero p {
            font-size: 1.5rem;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        /* Parallax GIF Section */
        .parallax-gif-section {
            height: 150vh;
            position: relative;
            background: #000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .parallax-gif {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            max-width: 80%;
            max-height: 80vh;
            z-index: 1;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }

        .content-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.4);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2;
        }

        .overlay-text {
            color: white;
            text-align: center;
            padding: 2rem;
            background: rgba(0,0,0,0.6);
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }

        .overlay-text h2 {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .overlay-text p {
            font-size: 1.2rem;
        }

        /* Content Sections */
        .content-section {
            min-height: 100vh;
            padding: 4rem 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }

        .section-1 {
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            color: white;
        }

        .section-2 {
            background: linear-gradient(45deg, #48cae4, #023e8a);
            color: white;
        }

        .section-3 {
            background: linear-gradient(45deg, #06ffa5, #3d5a80);
            color: white;
        }

        .section-content {
            max-width: 800px;
        }

        .section-content h2 {
            font-size: 3rem;
            margin-bottom: 2rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .section-content p {
            font-size: 1.3rem;
            line-height: 1.8;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        /* Scroll indicator */
        .scroll-indicator {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            color: white;
            font-size: 2rem;
            animation: bounce 2s infinite;
            z-index: 10;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateX(-50%) translateY(0);
            }
            40% {
                transform: translateX(-50%) translateY(-10px);
            }
            60% {
                transform: translateX(-50%) translateY(-5px);
            }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.5rem;
            }
            
            .hero p {
                font-size: 1.2rem;
            }
            
            .overlay-text h2 {
                font-size: 2rem;
            }
            
            .section-content h2 {
                font-size: 2rem;
            }
            
            .section-content p {
                font-size: 1.1rem;
            }
            
            .parallax-gif {
                max-width: 95%;
            }
        }
    </style>
</head>
<body>
    <!-- Hero Section -->
    <section class="hero">
        <div>
            <h1>Parallax Animation</h1>
            <p>Scroll down to experience the magic</p>
        </div>
        <div class="scroll-indicator">↓</div>
    </section>

    <!-- Parallax GIF Section -->
    <section class="parallax-gif-section">
        <img src="screenshot.gif" alt="Parallax GIF" class="parallax-gif" id="parallaxGif">
        <div class="content-overlay">
            <div class="overlay-text">
                <h2>Amazing Animation</h2>
                <p>Watch as this GIF moves with parallax effect</p>
            </div>
        </div>
    </section>

    <!-- Content Sections -->
    <section class="content-section section-1">
        <div class="section-content">
            <h2>Section One</h2>
            <p>This is the first content section. The parallax effect creates depth and visual interest as you scroll through the page. Notice how different elements move at different speeds.</p>
        </div>
    </section>

    <section class="content-section section-2">
        <div class="section-content">
            <h2>Section Two</h2>
            <p>Here's another section with beautiful gradients. The parallax scrolling effect makes the page feel more dynamic and engaging for users.</p>
        </div>
    </section>

    <section class="content-section section-3">
        <div class="section-content">
            <h2>Section Three</h2>
            <p>The final section showcases how parallax effects can enhance the storytelling aspect of your website, creating a more immersive experience.</p>
        </div>
    </section>

    <script>
        // Enhanced parallax effect with JavaScript
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const parallaxGif = document.getElementById('parallaxGif');
            const scrollIndicator = document.querySelector('.scroll-indicator');
            
            // Hide scroll indicator after scrolling
            if (scrolled > 100) {
                scrollIndicator.style.opacity = '0';
            } else {
                scrollIndicator.style.opacity = '1';
            }
            
            // Parallax effect for the GIF
            const speed = scrolled * 0.5;
            parallaxGif.style.transform = `translate(-50%, calc(-50% + ${speed}px)) scale(${1 + scrolled * 0.0005})`;
            
            // Add rotation effect
            const rotation = scrolled * 0.1;
            parallaxGif.style.filter = `hue-rotate(${rotation}deg)`;
        });

        // Smooth scrolling for better effect
        document.documentElement.style.scrollBehavior = 'smooth';
    </script>
</body>
</html>
